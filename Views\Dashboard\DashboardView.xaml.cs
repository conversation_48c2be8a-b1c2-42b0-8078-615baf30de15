using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using AqlanCenterProApp.ViewModels.Dashboard;
using AqlanCenterProApp.Views.Controls;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AqlanCenterProApp.Views.Dashboard
{
    /// <summary>
    /// Interaction logic for DashboardView.xaml
    /// </summary>
    public partial class DashboardView : UserControl
    {
        private readonly ILogger<DashboardView> _logger;
        private bool _isInitialized = false;
        private DashboardViewModel _viewModel;

        public DashboardView()
        {
            InitializeComponent();

            // الحصول على Logger من ServiceProvider
            try
            {
                var serviceProvider = Application.Current.Properties["ServiceProvider"] as IServiceProvider;
                _logger = serviceProvider?.GetService<ILogger<DashboardView>>();
            }
            catch
            {
                // في حالة فشل الحصول على Logger، نستخدم Console
                _logger = null;
            }
        }

        public DashboardView(DashboardViewModel viewModel) : this()
        {
            DataContext = viewModel;
            _viewModel = viewModel;
        }

        /// <summary>
        /// معالج حدث تحميل الواجهة
        /// </summary>
        private async void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            if (_isInitialized) return;

            try
            {
                _logger?.LogInformation("بدء تحميل DashboardView");

                if (DataContext is DashboardViewModel viewModel)
                {
                    _viewModel = viewModel;

                    // ربط أحداث QuickActionsPanel
                    ConnectQuickActionsPanelEvents();

                    // تحميل البيانات بشكل آمن في الخلفية
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await Task.Delay(100); // تأخير قصير للسماح للواجهة بالتحميل

                            await Application.Current.Dispatcher.InvokeAsync(async () =>
                            {
                                try
                                {
                                    await viewModel.InitializeAsync();
                                    _isInitialized = true;
                                    _logger?.LogInformation("تم تهيئة DashboardView بنجاح");
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogError(ex, "خطأ في تهيئة DashboardView");
                                    await HandleErrorAsync("حدث خطأ في تحميل بيانات الداشبورد", ex);
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "خطأ في تحميل بيانات الداشبورد");
                            await Application.Current.Dispatcher.InvokeAsync(() =>
                            {
                                HandleErrorAsync("حدث خطأ في تحميل البيانات", ex).Wait();
                            });
                        }
                    });
                }
                else
                {
                    _logger?.LogWarning("DataContext ليس من نوع DashboardViewModel");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في UserControl_Loaded");
                await HandleErrorAsync("حدث خطأ في تحميل الواجهة", ex);
            }
        }

        /// <summary>
        /// معالج حدث إغلاق الواجهة
        /// </summary>
        private void UserControl_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_viewModel != null)
                {
                    // تنظيف الموارد عند إغلاق الواجهة
                    _viewModel.Dispose();
                    _viewModel = null;
                    _isInitialized = false;
                    _logger?.LogInformation("تم تنظيف موارد DashboardView");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنظيف موارد DashboardView");
            }
        }

        /// <summary>
        /// معالجة الأخطاء وعرضها للمستخدم
        /// </summary>
        private async Task HandleErrorAsync(string userMessage, Exception ex)
        {
            try
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    if (_viewModel != null)
                    {
                        _viewModel.HasError = true;
                        _viewModel.ErrorMessage = $"{userMessage}: {ex.Message}";
                        _viewModel.IsLoading = false;
                    }
                });

                _logger?.LogError(ex, userMessage);
            }
            catch (Exception logEx)
            {
                _logger?.LogError(logEx, "خطأ في معالجة الخطأ");
            }
        }

        /// <summary>
        /// معالجة حدث تغيير حجم النافذة
        /// </summary>
        protected override void OnRenderSizeChanged(SizeChangedInfo sizeInfo)
        {
            base.OnRenderSizeChanged(sizeInfo);

            try
            {
                // تحسين الأداء عند تغيير حجم النافذة
                if (sizeInfo.WidthChanged || sizeInfo.HeightChanged)
                {
                    // يمكن إضافة منطق إضافي هنا لتحسين الأداء
                    _logger?.LogDebug($"تم تغيير حجم النافذة إلى: {sizeInfo.NewSize}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة تغيير حجم النافذة");
            }
        }

        /// <summary>
        /// معالجة حدث التركيز على الواجهة
        /// </summary>
        protected override void OnGotFocus(RoutedEventArgs e)
        {
            base.OnGotFocus(e);

            try
            {
                // تحديث البيانات عند التركيز على الواجهة
                if (_isInitialized && _viewModel != null)
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await _viewModel.RefreshDataAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "خطأ في تحديث البيانات عند التركيز");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة حدث التركيز");
            }
        }

        /// <summary>
        /// معالجة حدث فقدان التركيز
        /// </summary>
        protected override void OnLostFocus(RoutedEventArgs e)
        {
            base.OnLostFocus(e);

            try
            {
                // يمكن إضافة منطق إضافي هنا عند فقدان التركيز
                _logger?.LogDebug("فقدان التركيز على DashboardView");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة حدث فقدان التركيز");
            }
        }

        /// <summary>
        /// معالجة حدث الضغط على المفتاح
        /// </summary>
        protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
        {
            base.OnKeyDown(e);

            try
            {
                // تحديث البيانات عند الضغط على F5
                if (e.Key == System.Windows.Input.Key.F5 && _isInitialized && _viewModel != null)
                {
                    e.Handled = true;
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await _viewModel.RefreshDataAsync();
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "خطأ في تحديث البيانات عند الضغط على F5");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في معالجة حدث الضغط على المفتاح");
            }
        }

        /// <summary>
        /// معالج حدث إغلاق التطبيق
        /// </summary>
        private void OnApplicationExit(object sender, ExitEventArgs e)
        {
            try
            {
                if (_viewModel != null)
                {
                    _viewModel.Dispose();
                    _viewModel = null;
                }
                _logger?.LogInformation("تم تنظيف موارد DashboardView عند إغلاق التطبيق");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في تنظيف موارد DashboardView عند إغلاق التطبيق");
            }
        }

        /// <summary>
        /// معالج حدث النقر على زر إعادة تعبئة البيانات التجريبية
        /// </summary>
        private void ReseedDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reseedWindow = new ReseedDataWindow();
                var result = reseedWindow.ShowDialog();

                // إذا تم إعادة التعبئة بنجاح، قم بتحديث الداشبورد
                if (result == true && _viewModel != null)
                {
                    _ = Task.Run(async () =>
                    {
                        await Task.Delay(1000); // انتظار قصير للتأكد من اكتمال العملية
                        await Application.Current.Dispatcher.InvokeAsync(async () =>
                        {
                            await _viewModel.RefreshDataAsync();
                        });
                    });
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في فتح نافذة إعادة تعبئة البيانات");
                MessageBox.Show(
                    $"حدث خطأ في فتح نافذة إعادة تعبئة البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// ربط أحداث QuickActionsPanel بنظام التنقل
        /// </summary>
        private void ConnectQuickActionsPanelEvents()
        {
            try
            {
                // البحث عن QuickActionsPanel في الشجرة المرئية
                var quickActionsPanel = FindVisualChild<QuickActionsPanel>(this);
                if (quickActionsPanel != null)
                {
                    // ربط الأحداث بنظام التنقل
                    quickActionsPanel.AddPatientRequested += (s, e) =>
                        DashboardViewModel.NavigationRequested?.Invoke("AddPatient");

                    quickActionsPanel.AddAppointmentRequested += (s, e) =>
                        DashboardViewModel.NavigationRequested?.Invoke("AddAppointment");

                    quickActionsPanel.AddInvoiceRequested += (s, e) =>
                        DashboardViewModel.NavigationRequested?.Invoke("AddInvoice");

                    quickActionsPanel.ViewReportsRequested += (s, e) =>
                        DashboardViewModel.NavigationRequested?.Invoke("ReportsMain");

                    quickActionsPanel.ManageInventoryRequested += (s, e) =>
                        DashboardViewModel.NavigationRequested?.Invoke("Inventory");

                    quickActionsPanel.ManageEmployeesRequested += (s, e) =>
                        DashboardViewModel.NavigationRequested?.Invoke("Employees");

                    quickActionsPanel.OpenSettingsRequested += (s, e) =>
                        DashboardViewModel.NavigationRequested?.Invoke("SettingsMain");

                    _logger?.LogInformation("تم ربط أحداث QuickActionsPanel بنجاح");
                }
                else
                {
                    _logger?.LogWarning("لم يتم العثور على QuickActionsPanel");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "خطأ في ربط أحداث QuickActionsPanel");
            }
        }

        /// <summary>
        /// البحث عن عنصر فرعي في الشجرة المرئية
        /// </summary>
        private static T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }
    }
}

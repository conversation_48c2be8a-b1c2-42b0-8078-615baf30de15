#!/usr/bin/env pwsh

# Test database connection and initialization
Write-Host "Testing database connection..." -ForegroundColor Yellow

try {
    # Build the project first
    Write-Host "Building project..." -ForegroundColor Cyan
    dotnet build --configuration Release --verbosity quiet
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed" -ForegroundColor Red
        exit 1
    }

    Write-Host "Build successful" -ForegroundColor Green
    
    # Check if database file exists
    $dbPath = "AqlanCenterDatabase.db"
    if (Test-Path $dbPath) {
        Write-Host "Database file exists: $dbPath" -ForegroundColor Green
        $dbSize = (Get-Item $dbPath).Length
        Write-Host "Database size: $($dbSize / 1KB) KB" -ForegroundColor Cyan
    } else {
        Write-Host "Database file not found: $dbPath" -ForegroundColor Red
    }
    
    # Try to run the application with timeout
    Write-Host "Starting application..." -ForegroundColor Cyan
    
    $process = Start-Process -FilePath "dotnet" -ArgumentList "run --configuration Release" -PassThru -NoNewWindow
    
    # Wait for 10 seconds to see if it starts properly
    Start-Sleep -Seconds 10
    
    if (!$process.HasExited) {
        Write-Host "Application started successfully" -ForegroundColor Green
        Write-Host "Stopping application..." -ForegroundColor Yellow
        $process.Kill()
        $process.WaitForExit()
        Write-Host "Application stopped" -ForegroundColor Green
    } else {
        Write-Host "Application exited with code: $($process.ExitCode)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "Database test completed" -ForegroundColor Green
